import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  Paper,
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import HydrojugLogo from '../../assets/HydrojugLogo.webp';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login, demoLogin } = useAuth();
  const navigate = useNavigate();

  async function handleSubmit(e) {
    e.preventDefault();

    try {
      setError('');
      setLoading(true);
      await login(email, password);
      navigate('/');
    } catch (error) {
      setError('Failed to log in. Please check your credentials.');
      console.error('Login error:', error);
    }

    setLoading(false);
  }

  const handleDemoLogin = async (role) => {
    const demoCredentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      manager: { email: '<EMAIL>', password: 'manager123' },
      viewer: { email: '<EMAIL>', password: 'viewer123' }
    };

    const creds = demoCredentials[role];
    setEmail(creds.email);
    setPassword(creds.password);
    
    try {
      setError('');
      setLoading(true);
      await demoLogin(creds.email, role);
      navigate('/');
    } catch (error) {
      setError('Demo login failed. Please try manual login.');
      console.error('Demo login error:', error);
    }
    setLoading(false);
  };

  return (
    <Box
      sx={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        padding: 2,
      }}
    >
      <Paper
        elevation={3}
        sx={{
          padding: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          width: '100%',
          maxWidth: 450,
          borderRadius: 2,
        }}
      >
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <img 
            src={HydrojugLogo} 
            alt="Hydrojug" 
            style={{ height: 60, width: 'auto', marginBottom: 16 }}
          />
          <Typography component="h1" variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            Supply Chain Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Sign in to access your dashboard
          </Typography>
        </Box>

        {error && <Alert severity="error" sx={{ width: '100%', mb: 2 }}>{error}</Alert>}

        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            autoComplete="email"
            autoFocus
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            autoComplete="current-password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, py: 1.5 }}
            disabled={loading}
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </Button>
        </Box>

        <Box sx={{ mt: 2, width: '100%' }}>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
            Demo Accounts (for testing):
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleDemoLogin('admin')}
              disabled={loading}
            >
              Admin Demo
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleDemoLogin('manager')}
              disabled={loading}
            >
              Manager Demo
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleDemoLogin('viewer')}
              disabled={loading}
            >
              Viewer Demo
            </Button>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
}

export default Login;
