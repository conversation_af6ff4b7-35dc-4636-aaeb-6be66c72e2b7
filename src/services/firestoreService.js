// Firestore Service for Supply Chain Data Management
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot 
} from 'firebase/firestore';
import { db } from '../config/firebase';

class FirestoreService {
  // Products/Inventory Operations
  async getProducts() {
    try {
      const productsRef = collection(db, 'products');
      const snapshot = await getDocs(productsRef);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  async getProduct(productId) {
    try {
      const productRef = doc(db, 'products', productId);
      const snapshot = await getDoc(productRef);
      if (snapshot.exists()) {
        return { id: snapshot.id, ...snapshot.data() };
      }
      return null;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  async addProduct(productData) {
    try {
      const productsRef = collection(db, 'products');
      const docRef = await addDoc(productsRef, {
        ...productData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  }

  async updateProduct(productId, productData) {
    try {
      const productRef = doc(db, 'products', productId);
      await updateDoc(productRef, {
        ...productData,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  async deleteProduct(productId) {
    try {
      const productRef = doc(db, 'products', productId);
      await deleteDoc(productRef);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  // Low stock alerts
  async getLowStockProducts() {
    try {
      const productsRef = collection(db, 'products');
      const q = query(productsRef, where('currentStock', '<=', 'reorderPoint'));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw error;
    }
  }

  // Suppliers Operations
  async getSuppliers() {
    try {
      const suppliersRef = collection(db, 'suppliers');
      const snapshot = await getDocs(suppliersRef);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      throw error;
    }
  }

  async addSupplier(supplierData) {
    try {
      const suppliersRef = collection(db, 'suppliers');
      const docRef = await addDoc(suppliersRef, {
        ...supplierData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding supplier:', error);
      throw error;
    }
  }

  async updateSupplier(supplierId, supplierData) {
    try {
      const supplierRef = doc(db, 'suppliers', supplierId);
      await updateDoc(supplierRef, {
        ...supplierData,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating supplier:', error);
      throw error;
    }
  }

  // Orders Operations
  async getOrders() {
    try {
      const ordersRef = collection(db, 'orders');
      const q = query(ordersRef, orderBy('orderDate', 'desc'));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  async addOrder(orderData) {
    try {
      const ordersRef = collection(db, 'orders');
      const docRef = await addDoc(ordersRef, {
        ...orderData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding order:', error);
      throw error;
    }
  }

  async updateOrderStatus(orderId, status) {
    try {
      const orderRef = doc(db, 'orders', orderId);
      await updateDoc(orderRef, {
        status,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Inventory Movements
  async addInventoryMovement(movementData) {
    try {
      const movementsRef = collection(db, 'movements');
      const docRef = await addDoc(movementsRef, {
        ...movementData,
        timestamp: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding inventory movement:', error);
      throw error;
    }
  }

  async getInventoryMovements(productId = null, limit = 50) {
    try {
      const movementsRef = collection(db, 'movements');
      let q = query(movementsRef, orderBy('timestamp', 'desc'), limit(limit));
      
      if (productId) {
        q = query(movementsRef, where('productId', '==', productId), orderBy('timestamp', 'desc'), limit(limit));
      }
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching inventory movements:', error);
      throw error;
    }
  }

  // Real-time subscriptions
  subscribeToProducts(callback) {
    const productsRef = collection(db, 'products');
    return onSnapshot(productsRef, (snapshot) => {
      const products = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      callback(products);
    });
  }

  subscribeToOrders(callback) {
    const ordersRef = collection(db, 'orders');
    const q = query(ordersRef, orderBy('orderDate', 'desc'));
    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      callback(orders);
    });
  }

  subscribeToLowStockAlerts(callback) {
    const productsRef = collection(db, 'products');
    return onSnapshot(productsRef, (snapshot) => {
      const lowStockProducts = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(product => product.currentStock <= product.reorderPoint);
      callback(lowStockProducts);
    });
  }

  // Dashboard metrics
  async getDashboardMetrics() {
    try {
      const [products, orders, suppliers] = await Promise.all([
        this.getProducts(),
        this.getOrders(),
        this.getSuppliers()
      ]);

      const totalInventoryValue = products.reduce((sum, product) => 
        sum + (product.currentStock * product.unitCost), 0
      );

      const lowStockCount = products.filter(product => 
        product.currentStock <= product.reorderPoint
      ).length;

      const pendingOrders = orders.filter(order => 
        order.status === 'pending' || order.status === 'confirmed'
      ).length;

      const averageSupplierPerformance = suppliers.reduce((sum, supplier) => 
        sum + (supplier.performanceScore || 0), 0
      ) / suppliers.length;

      return {
        totalInventoryValue,
        lowStockCount,
        pendingOrders,
        averageSupplierPerformance: Math.round(averageSupplierPerformance)
      };
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      throw error;
    }
  }

  // Seed data for demo purposes
  async seedDemoData() {
    try {
      // This would populate the database with sample data for demonstration
      console.log('Seeding demo data...');
      
      // Add sample products
      const sampleProducts = [
        {
          sku: 'HJ-CL-32-BLU',
          name: 'HydroJug Classic 32oz',
          category: 'Water Bottles',
          currentStock: 15,
          reorderPoint: 25,
          maxStock: 100,
          unitCost: 24.99,
          location: 'Warehouse A',
          supplierId: 'supplier1'
        },
        // Add more sample data as needed
      ];

      for (const product of sampleProducts) {
        await this.addProduct(product);
      }

      console.log('Demo data seeded successfully');
    } catch (error) {
      console.error('Error seeding demo data:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const firestoreService = new FirestoreService();
export default firestoreService;
