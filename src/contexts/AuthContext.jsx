import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  createUserWithEmailAndPassword 
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const [demoMode, setDemoMode] = useState(false);

  // Initialize loading state
  React.useEffect(() => {
    // Set loading to false after a brief moment to allow for demo mode
    const timer = setTimeout(() => {
      if (!currentUser && !demoMode) {
        setLoading(false);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [currentUser, demoMode]);

  async function signup(email, password, userData) {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      role: userData.role || 'viewer',
      name: userData.name || '',
      department: userData.department || '',
      createdAt: new Date()
    });
    
    return userCredential;
  }

  function login(email, password) {
    return signInWithEmailAndPassword(auth, email, password);
  }

  function demoLogin(email, role = 'viewer') {
    // For demo purposes, create a mock user object
    const mockUser = {
      uid: `demo-${role}`,
      email: email,
      displayName: `Demo ${role.charAt(0).toUpperCase() + role.slice(1)}`,
    };

    setCurrentUser(mockUser);
    setUserRole(role);
    setDemoMode(true);
    setLoading(false);

    return Promise.resolve({ user: mockUser });
  }

  function logout() {
    if (demoMode) {
      setCurrentUser(null);
      setUserRole(null);
      setDemoMode(false);
      return Promise.resolve();
    }
    return signOut(auth);
  }

  async function getUserRole(uid) {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data().role;
      }
      return 'viewer'; // default role
    } catch (error) {
      console.error('Error fetching user role:', error);
      return 'viewer';
    }
  }

  useEffect(() => {
    // Skip Firebase auth listener if in demo mode
    if (demoMode) {
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      if (user) {
        const role = await getUserRole(user.uid);
        setUserRole(role);
      } else {
        setUserRole(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, [demoMode]);

  const value = {
    currentUser,
    userRole,
    signup,
    login,
    demoLogin,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
