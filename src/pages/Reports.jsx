import React, { useState } from 'react';
import {
  Box,
  <PERSON><PERSON>graphy,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Download,
  PictureAsPdf,
  TableChart,
  Email,
  Schedule,
  Assessment,
  Inventory,
  People,
  TrendingUp,
} from '@mui/icons-material';

// Mock reports data
const availableReports = [
  {
    id: 1,
    name: 'Monthly Inventory Summary',
    description: 'Comprehensive overview of inventory levels, movements, and valuations',
    category: 'Inventory',
    lastGenerated: '2024-01-15',
    frequency: 'Monthly',
    icon: <Inventory />,
    formats: ['PDF', 'Excel'],
  },
  {
    id: 2,
    name: 'Supplier Performance Report',
    description: 'Analysis of supplier delivery times, quality ratings, and performance metrics',
    category: 'Suppliers',
    lastGenerated: '2024-01-14',
    frequency: 'Weekly',
    icon: <People />,
    formats: ['PDF', 'Excel'],
  },
  {
    id: 3,
    name: 'Cost Analysis Report',
    description: 'Detailed breakdown of procurement costs, trends, and optimization opportunities',
    category: 'Financial',
    lastGenerated: '2024-01-13',
    frequency: 'Monthly',
    icon: <Assessment />,
    formats: ['PDF', 'Excel'],
  },
  {
    id: 4,
    name: 'Demand Forecasting Report',
    description: 'Predictive analysis of future inventory needs based on historical data',
    category: 'Analytics',
    lastGenerated: '2024-01-12',
    frequency: 'Quarterly',
    icon: <TrendingUp />,
    formats: ['PDF', 'Excel'],
  },
];

const recentReports = [
  {
    id: 1,
    name: 'January 2024 Inventory Summary',
    type: 'Monthly Inventory Summary',
    generatedDate: '2024-01-15',
    generatedBy: 'System',
    size: '2.4 MB',
    format: 'PDF',
    status: 'completed',
  },
  {
    id: 2,
    name: 'Week 2 Supplier Performance',
    type: 'Supplier Performance Report',
    generatedDate: '2024-01-14',
    generatedBy: 'John Smith',
    size: '1.8 MB',
    format: 'Excel',
    status: 'completed',
  },
  {
    id: 3,
    name: 'Q4 2023 Cost Analysis',
    type: 'Cost Analysis Report',
    generatedDate: '2024-01-10',
    generatedBy: 'Sarah Johnson',
    size: '3.2 MB',
    format: 'PDF',
    status: 'completed',
  },
];

function Reports() {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTimeRange, setSelectedTimeRange] = useState('last_month');

  const categories = [...new Set(availableReports.map(report => report.category))];

  const filteredReports = selectedCategory 
    ? availableReports.filter(report => report.category === selectedCategory)
    : availableReports;

  const handleGenerateReport = (reportId, format) => {
    console.log(`Generating report ${reportId} in ${format} format`);
    // Here you would implement the actual report generation logic
  };

  const handleDownloadReport = (reportId) => {
    console.log(`Downloading report ${reportId}`);
    // Here you would implement the download logic
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Reports & Analytics
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={selectedCategory}
              label="Category"
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <MenuItem value="">All Categories</MenuItem>
              {categories.map(category => (
                <MenuItem key={category} value={category}>{category}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={selectedTimeRange}
              label="Time Range"
              onChange={(e) => setSelectedTimeRange(e.target.value)}
            >
              <MenuItem value="last_week">Last Week</MenuItem>
              <MenuItem value="last_month">Last Month</MenuItem>
              <MenuItem value="last_quarter">Last Quarter</MenuItem>
              <MenuItem value="last_year">Last Year</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Available Reports
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {availableReports.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Generated This Month
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                {recentReports.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Scheduled Reports
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                5
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Downloads
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                142
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Available Reports */}
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
        Available Reports
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {filteredReports.map((report) => (
          <Grid item xs={12} md={6} lg={4} key={report.id}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Box sx={{ color: 'primary.main' }}>
                    {report.icon}
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {report.name}
                    </Typography>
                    <Chip label={report.category} size="small" color="primary" variant="outlined" />
                  </Box>
                </Box>

                <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                  {report.description}
                </Typography>

                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="body2" color="textSecondary">
                    Frequency: {report.frequency}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Last: {formatDate(report.lastGenerated)}
                  </Typography>
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  {report.formats.map((format) => (
                    <Button
                      key={format}
                      size="small"
                      variant="outlined"
                      startIcon={format === 'PDF' ? <PictureAsPdf /> : <TableChart />}
                      onClick={() => handleGenerateReport(report.id, format)}
                    >
                      Generate {format}
                    </Button>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent Reports */}
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
        Recent Reports
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Report Name</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Generated Date</TableCell>
              <TableCell>Generated By</TableCell>
              <TableCell>Format</TableCell>
              <TableCell>Size</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {recentReports.map((report) => (
              <TableRow key={report.id} hover>
                <TableCell sx={{ fontWeight: 500 }}>
                  {report.name}
                </TableCell>
                <TableCell>{report.type}</TableCell>
                <TableCell>{formatDate(report.generatedDate)}</TableCell>
                <TableCell>{report.generatedBy}</TableCell>
                <TableCell>
                  <Chip
                    label={report.format}
                    size="small"
                    color={report.format === 'PDF' ? 'primary' : 'secondary'}
                  />
                </TableCell>
                <TableCell>{report.size}</TableCell>
                <TableCell>
                  <Chip
                    label={report.status}
                    size="small"
                    color={getStatusColor(report.status)}
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleDownloadReport(report.id)}
                    color="primary"
                  >
                    <Download />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="primary"
                  >
                    <Email />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Scheduled Reports Section */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          Scheduled Reports
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Schedule color="primary" />
            <Typography variant="h6">Automated Report Generation</Typography>
          </Box>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Set up automated report generation and delivery schedules for regular business insights.
          </Typography>
          <Button variant="outlined" startIcon={<Schedule />}>
            Configure Schedules
          </Button>
        </Paper>
      </Box>
    </Box>
  );
}

export default Reports;
