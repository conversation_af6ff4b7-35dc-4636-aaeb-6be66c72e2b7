import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Tabs,
  Tab,
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Mock analytics data
const inventoryTrendData = [
  { month: 'Jan', stock: 2400, value: 240000, orders: 12 },
  { month: 'Feb', stock: 2210, value: 221000, orders: 15 },
  { month: 'Mar', stock: 2290, value: 229000, orders: 18 },
  { month: 'Apr', stock: 2000, value: 200000, orders: 22 },
  { month: 'May', stock: 2181, value: 218100, orders: 19 },
  { month: 'Jun', stock: 2500, value: 250000, orders: 16 },
  { month: 'Jul', stock: 2100, value: 210000, orders: 20 },
  { month: 'Aug', stock: 2300, value: 230000, orders: 17 },
  { month: 'Sep', stock: 2450, value: 245000, orders: 14 },
  { month: 'Oct', stock: 2600, value: 260000, orders: 13 },
  { month: 'Nov', stock: 2400, value: 240000, orders: 18 },
  { month: 'Dec', stock: 2800, value: 280000, orders: 21 },
];

const supplierPerformanceData = [
  { name: 'ABC Manufacturing', performance: 92, deliveryTime: 5.2, orders: 45 },
  { name: 'XYZ Corporation', performance: 87, deliveryTime: 6.8, orders: 32 },
  { name: 'Parts Plus Inc', performance: 78, deliveryTime: 8.1, orders: 28 },
  { name: 'Quality Components', performance: 94, deliveryTime: 4.5, orders: 38 },
  { name: 'Reliable Supplies', performance: 82, deliveryTime: 7.2, orders: 25 },
];

const categoryDistribution = [
  { name: 'Water Bottles', value: 65, color: '#1976d2' },
  { name: 'Lids', value: 20, color: '#26c6da' },
  { name: 'Straws', value: 10, color: '#4caf50' },
  { name: 'Accessories', value: 5, color: '#ff9800' },
];

const demandForecastData = [
  { month: 'Jan', actual: 2400, predicted: 2350, confidence: 95 },
  { month: 'Feb', actual: 2210, predicted: 2180, confidence: 92 },
  { month: 'Mar', actual: 2290, predicted: 2320, confidence: 88 },
  { month: 'Apr', actual: 2000, predicted: 2050, confidence: 90 },
  { month: 'May', actual: 2181, predicted: 2200, confidence: 94 },
  { month: 'Jun', actual: 2500, predicted: 2480, confidence: 91 },
  { month: 'Jul', actual: null, predicted: 2350, confidence: 87 },
  { month: 'Aug', actual: null, predicted: 2420, confidence: 85 },
  { month: 'Sep', actual: null, predicted: 2380, confidence: 83 },
];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function Analytics() {
  const [timeRange, setTimeRange] = useState('12months');
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Supply Chain Analytics
        </Typography>
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="3months">Last 3 Months</MenuItem>
            <MenuItem value="6months">Last 6 Months</MenuItem>
            <MenuItem value="12months">Last 12 Months</MenuItem>
            <MenuItem value="24months">Last 24 Months</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Key Metrics Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inventory Turnover
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                4.2x
              </Typography>
              <Typography variant="body2" color="success.main">
                +12% vs last period
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Lead Time
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                6.2 days
              </Typography>
              <Typography variant="body2" color="success.main">
                -8% improvement
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Stockout Rate
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                2.1%
              </Typography>
              <Typography variant="body2" color="error.main">
                +0.3% vs target
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Cost Savings
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                $45K
              </Typography>
              <Typography variant="body2" color="success.main">
                This quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Analytics Tabs */}
      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="analytics tabs">
            <Tab label="Inventory Trends" />
            <Tab label="Supplier Performance" />
            <Tab label="Demand Forecasting" />
            <Tab label="Cost Analysis" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Inventory Levels Over Time
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={inventoryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="stock"
                        stroke="#1976d2"
                        fill="#1976d2"
                        fillOpacity={0.3}
                        name="Stock Units"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} lg={4}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Category Distribution
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={categoryDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {categoryDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Inventory Value and Order Frequency
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={inventoryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="value" fill="#26c6da" name="Inventory Value ($)" />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="orders"
                        stroke="#ff9800"
                        strokeWidth={3}
                        name="Orders Count"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Supplier Performance Comparison
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={supplierPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="performance" fill="#4caf50" name="Performance Score %" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} lg={4}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Delivery Time Analysis
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={supplierPerformanceData} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip />
                      <Bar dataKey="deliveryTime" fill="#ff9800" name="Avg Delivery (days)" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Demand Forecasting with Confidence Intervals
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={demandForecastData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="actual"
                    stroke="#1976d2"
                    strokeWidth={3}
                    name="Actual Demand"
                    connectNulls={false}
                  />
                  <Line
                    type="monotone"
                    dataKey="predicted"
                    stroke="#ff9800"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Predicted Demand"
                  />
                </LineChart>
              </ResponsiveContainer>
              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                * Forecasting model uses historical data, seasonal patterns, and market trends to predict future demand.
                Confidence levels are shown as percentages.
              </Typography>
            </CardContent>
          </Card>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Cost Breakdown by Category
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Raw Materials', value: 45, color: '#1976d2' },
                          { name: 'Manufacturing', value: 30, color: '#26c6da' },
                          { name: 'Shipping', value: 15, color: '#4caf50' },
                          { name: 'Storage', value: 10, color: '#ff9800' },
                        ]}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {categoryDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                    Monthly Cost Trends
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={inventoryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Area
                        type="monotone"
                        dataKey="value"
                        stroke="#f44336"
                        fill="#f44336"
                        fillOpacity={0.3}
                        name="Total Cost ($)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </Box>
  );
}

export default Analytics;
