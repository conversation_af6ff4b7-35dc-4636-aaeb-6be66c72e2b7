import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Rating,
  LinearProgress,
} from '@mui/material';
import {
  Add,
  Edit,
  Phone,
  Email,
} from '@mui/icons-material';

// Mock supplier data
const mockSuppliers = [
  {
    id: 1,
    name: 'ABC Manufacturing',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Industrial Blvd, Manufacturing City, MC 12345',
    performanceScore: 92,
    averageDeliveryTime: 5.2,
    totalOrders: 45,
    onTimeDelivery: 89,
    qualityRating: 4.5,
    products: ['Water Bottles', 'Lids'],
    status: 'active',
  },
  {
    id: 2,
    name: 'XYZ Corporation',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Business Park Dr, Commerce Town, CT 67890',
    performanceScore: 87,
    averageDeliveryTime: 6.8,
    totalOrders: 32,
    onTimeDelivery: 84,
    qualityRating: 4.2,
    products: ['Water Bottles', 'Accessories'],
    status: 'active',
  },
  {
    id: 3,
    name: 'Parts Plus Inc',
    contactPerson: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Component Ave, Parts Valley, PV 13579',
    performanceScore: 78,
    averageDeliveryTime: 8.1,
    totalOrders: 28,
    onTimeDelivery: 75,
    qualityRating: 3.8,
    products: ['Accessories', 'Straws', 'Lids'],
    status: 'active',
  },
];

function Suppliers() {
  const [suppliers, setSuppliers] = useState(mockSuppliers);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 80) return 'warning';
    return 'error';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const handleEditSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedSupplier(null);
  };

  const averagePerformance = suppliers.reduce((sum, supplier) => sum + supplier.performanceScore, 0) / suppliers.length;
  const averageDeliveryTime = suppliers.reduce((sum, supplier) => sum + supplier.averageDeliveryTime, 0) / suppliers.length;

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Supplier Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpenDialog(true)}
        >
          Add Supplier
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Suppliers
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {suppliers.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Average Performance
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                {averagePerformance.toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Delivery Time
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {averageDeliveryTime.toFixed(1)} days
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Suppliers
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                {suppliers.filter(s => s.status === 'active').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Suppliers Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {suppliers.map((supplier) => (
          <Grid size={{ xs: 12, md: 6, lg: 4 }} key={supplier.id}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {supplier.name}
                  </Typography>
                  <Chip
                    label={supplier.status}
                    color={getStatusColor(supplier.status)}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Contact: {supplier.contactPerson}
                </Typography>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Email sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">{supplier.email}</Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Phone sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">{supplier.phone}</Typography>
                </Box>

                <Box mb={2}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Performance Score
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LinearProgress
                      variant="determinate"
                      value={supplier.performanceScore}
                      sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                      color={getPerformanceColor(supplier.performanceScore)}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {supplier.performanceScore}%
                    </Typography>
                  </Box>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid size={6}>
                    <Typography variant="body2" color="textSecondary">
                      Avg Delivery
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {supplier.averageDeliveryTime} days
                    </Typography>
                  </Grid>
                  <Grid size={6}>
                    <Typography variant="body2" color="textSecondary">
                      On-Time Rate
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {supplier.onTimeDelivery}%
                    </Typography>
                  </Grid>
                </Grid>

                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Typography variant="body2" color="textSecondary">
                    Quality Rating:
                  </Typography>
                  <Rating value={supplier.qualityRating} readOnly size="small" />
                  <Typography variant="body2">({supplier.qualityRating})</Typography>
                </Box>

                <Box mb={2}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Products
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {supplier.products.map((product) => (
                      <Chip key={product} label={product} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="textSecondary">
                    {supplier.totalOrders} total orders
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => handleEditSupplier(supplier)}
                    color="primary"
                  >
                    <Edit />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Detailed Table */}
      <Paper sx={{ mb: 3 }}>
        <Box p={2}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Supplier Performance Details
          </Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Supplier</TableCell>
                <TableCell>Contact</TableCell>
                <TableCell align="right">Performance</TableCell>
                <TableCell align="right">Delivery Time</TableCell>
                <TableCell align="right">On-Time Rate</TableCell>
                <TableCell align="right">Quality</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {suppliers.map((supplier) => (
                <TableRow key={supplier.id} hover>
                  <TableCell>
                    <Typography sx={{ fontWeight: 500 }}>{supplier.name}</Typography>
                  </TableCell>
                  <TableCell>{supplier.contactPerson}</TableCell>
                  <TableCell align="right">
                    <Chip
                      label={`${supplier.performanceScore}%`}
                      color={getPerformanceColor(supplier.performanceScore)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">{supplier.averageDeliveryTime} days</TableCell>
                  <TableCell align="right">{supplier.onTimeDelivery}%</TableCell>
                  <TableCell align="right">
                    <Rating value={supplier.qualityRating} readOnly size="small" />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={supplier.status}
                      color={getStatusColor(supplier.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleEditSupplier(supplier)}
                      color="primary"
                    >
                      <Edit />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedSupplier ? 'Edit Supplier' : 'Add New Supplier'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Supplier Name"
                defaultValue={selectedSupplier?.name || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Contact Person"
                defaultValue={selectedSupplier?.contactPerson || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                defaultValue={selectedSupplier?.email || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Phone"
                defaultValue={selectedSupplier?.phone || ''}
              />
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="Address"
                multiline
                rows={2}
                defaultValue={selectedSupplier?.address || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleCloseDialog}>
            {selectedSupplier ? 'Update' : 'Add'} Supplier
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Suppliers;
