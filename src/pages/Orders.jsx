import { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import {
  Add,
  Edit,
  Visibility,
  LocalShipping,
  CheckCircle,
  Schedule,
  Warning,
} from '@mui/icons-material';

// Mock orders data
const mockOrders = [
  {
    id: 1,
    orderNumber: 'PO-2024-001',
    supplier: 'ABC Manufacturing',
    items: [
      { name: 'HydroJug Classic 32oz', quantity: 100, unitPrice: 24.99 },
      { name: 'Standard Lid', quantity: 100, unitPrice: 4.99 },
    ],
    totalAmount: 2998,
    status: 'delivered',
    orderDate: '2024-01-10',
    expectedDelivery: '2024-01-20',
    actualDelivery: '2024-01-18',
    priority: 'normal',
  },
  {
    id: 2,
    orderNumber: 'PO-2024-002',
    supplier: 'XYZ Corporation',
    items: [
      { name: 'HydroJug Pro 40oz', quantity: 75, unitPrice: 29.99 },
    ],
    totalAmount: 2249.25,
    status: 'in_transit',
    orderDate: '2024-01-15',
    expectedDelivery: '2024-01-25',
    actualDelivery: null,
    priority: 'high',
  },
  {
    id: 3,
    orderNumber: 'PO-2024-003',
    supplier: 'Parts Plus Inc',
    items: [
      { name: 'Silicone Straw', quantity: 200, unitPrice: 2.99 },
      { name: 'Standard Lid', quantity: 50, unitPrice: 4.99 },
    ],
    totalAmount: 847.5,
    status: 'pending',
    orderDate: '2024-01-20',
    expectedDelivery: '2024-01-30',
    actualDelivery: null,
    priority: 'normal',
  },
];

const orderStatuses = [
  { value: 'pending', label: 'Pending', color: 'warning', icon: <Schedule /> },
  { value: 'confirmed', label: 'Confirmed', color: 'info', icon: <CheckCircle /> },
  { value: 'in_transit', label: 'In Transit', color: 'primary', icon: <LocalShipping /> },
  { value: 'delivered', label: 'Delivered', color: 'success', icon: <CheckCircle /> },
  { value: 'delayed', label: 'Delayed', color: 'error', icon: <Warning /> },
];

const orderSteps = ['Order Placed', 'Confirmed', 'In Production', 'Shipped', 'Delivered'];

function Orders() {
  const [orders, setOrders] = useState(mockOrders);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'details'

  const getStatusInfo = (status) => {
    return orderStatuses.find(s => s.value === status) || orderStatuses[0];
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getActiveStep = (status) => {
    switch (status) {
      case 'pending': return 0;
      case 'confirmed': return 1;
      case 'in_transit': return 3;
      case 'delivered': return 4;
      default: return 0;
    }
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setViewMode('details');
    setOpenDialog(true);
  };

  const handleEditOrder = (order) => {
    setSelectedOrder(order);
    setViewMode('edit');
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedOrder(null);
    setViewMode('list');
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const totalOrderValue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const inTransitOrders = orders.filter(order => order.status === 'in_transit').length;

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
        gap={{ xs: 2, sm: 0 }}
        mb={3}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Order Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpenDialog(true)}
          size="small"
          sx={{
            alignSelf: { xs: 'stretch', sm: 'auto' }
          }}
        >
          Create Order
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Orders
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {orders.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending Orders
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                {pendingOrders}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Transit
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                {inTransitOrders}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Value
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                {formatCurrency(totalOrderValue)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Orders Table */}
      <TableContainer
        component={Paper}
        sx={{
          overflowX: 'auto',
          '& .MuiTable-root': {
            minWidth: { xs: 800, sm: 'auto' }
          }
        }}
      >
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Order Number
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Supplier
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Order Date
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Expected Delivery
              </TableCell>
              <TableCell
                align="right"
                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
              >
                Total Amount
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Priority
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Status
              </TableCell>
              <TableCell sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.map((order) => {
              const statusInfo = getStatusInfo(order.status);
              return (
                <TableRow key={order.id} hover>
                  <TableCell
                    sx={{
                      fontFamily: 'monospace',
                      fontWeight: 600,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '8px', sm: '16px' }
                    }}
                  >
                    {order.orderNumber}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '8px', sm: '16px' }
                    }}
                  >
                    {order.supplier}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '8px', sm: '16px' }
                    }}
                  >
                    {formatDate(order.orderDate)}
                  </TableCell>
                  <TableCell
                    sx={{
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '8px', sm: '16px' }
                    }}
                  >
                    {formatDate(order.expectedDelivery)}
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{
                      fontWeight: 600,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      padding: { xs: '8px', sm: '16px' }
                    }}
                  >
                    {formatCurrency(order.totalAmount)}
                  </TableCell>
                  <TableCell sx={{ padding: { xs: '8px', sm: '16px' } }}>
                    <Chip
                      label={order.priority}
                      color={getPriorityColor(order.priority)}
                      size="small"
                      sx={{
                        fontSize: { xs: '0.6875rem', sm: '0.75rem' },
                        height: { xs: 20, sm: 24 }
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ padding: { xs: '8px', sm: '16px' } }}>
                    <Chip
                      label={statusInfo.label}
                      color={statusInfo.color}
                      size="small"
                      icon={statusInfo.icon}
                      sx={{
                        fontSize: { xs: '0.6875rem', sm: '0.75rem' },
                        height: { xs: 20, sm: 24 }
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ padding: { xs: '4px', sm: '16px' } }}>
                    <Box sx={{ display: 'flex', gap: { xs: 0.5, sm: 1 } }}>
                      <IconButton
                        size="small"
                        onClick={() => handleViewOrder(order)}
                        color="primary"
                        sx={{
                          padding: { xs: '4px', sm: '8px' },
                          '& .MuiSvgIcon-root': {
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                          }
                        }}
                      >
                        <Visibility />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleEditOrder(order)}
                        color="primary"
                        sx={{
                          padding: { xs: '4px', sm: '8px' },
                          '& .MuiSvgIcon-root': {
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                          }
                        }}
                      >
                        <Edit />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Order Details/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {viewMode === 'details' ? 'Order Details' : 
           selectedOrder ? 'Edit Order' : 'Create New Order'}
        </DialogTitle>
        <DialogContent>
          {viewMode === 'details' && selectedOrder ? (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Order Number
                  </Typography>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                    {selectedOrder.orderNumber}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Supplier
                  </Typography>
                  <Typography variant="body1">{selectedOrder.supplier}</Typography>
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Order Date
                  </Typography>
                  <Typography variant="body1">{formatDate(selectedOrder.orderDate)}</Typography>
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Expected Delivery
                  </Typography>
                  <Typography variant="body1">{formatDate(selectedOrder.expectedDelivery)}</Typography>
                </Grid>
              </Grid>

              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Order Progress
              </Typography>
              <Stepper activeStep={getActiveStep(selectedOrder.status)} sx={{ mb: 3 }}>
                {orderSteps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Order Items
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Item</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                      <TableCell align="right">Unit Price</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedOrder.items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.name}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">{formatCurrency(item.unitPrice)}</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 600 }}>
                          {formatCurrency(item.quantity * item.unitPrice)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={3} sx={{ fontWeight: 600 }}>
                        Total Order Value
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
                        {formatCurrency(selectedOrder.totalAmount)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          ) : (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="Order Number"
                  defaultValue={selectedOrder?.orderNumber || ''}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <FormControl fullWidth>
                  <InputLabel>Supplier</InputLabel>
                  <Select
                    defaultValue={selectedOrder?.supplier || ''}
                    label="Supplier"
                  >
                    <MenuItem value="ABC Manufacturing">ABC Manufacturing</MenuItem>
                    <MenuItem value="XYZ Corporation">XYZ Corporation</MenuItem>
                    <MenuItem value="Parts Plus Inc">Parts Plus Inc</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="Expected Delivery"
                  type="date"
                  defaultValue={selectedOrder?.expectedDelivery || ''}
                  slotProps={{
                    inputLabel: { shrink: true }
                  }}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select
                    defaultValue={selectedOrder?.priority || 'normal'}
                    label="Priority"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="normal">Normal</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {viewMode === 'details' ? 'Close' : 'Cancel'}
          </Button>
          {viewMode !== 'details' && (
            <Button variant="contained" onClick={handleCloseDialog}>
              {selectedOrder ? 'Update' : 'Create'} Order
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Orders;
