import { useState } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
} from '@mui/material';
import {
  Inventory,
  ShoppingCart,
  TrendingUp,
  Add,
  Assessment,
  LocalShipping,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

// Mock data for demonstration
const mockMetrics = {
  totalInventoryValue: 2450000,
  lowStockAlerts: 12,
  pendingOrders: 8,
  activeSuppliers: 15,
};

// Mock alerts data
const mockAlerts = [
  {
    id: 1,
    type: 'error',
    message: 'HydroJug Classic 32oz will run out in 3 days',
    action: 'Order by Dec 12th',
  },
  {
    id: 2,
    type: 'warning',
    message: 'HydroJug Pro 40oz will run out in 8 days',
    action: 'Order by Dec 15th',
  },
  {
    id: 3,
    type: 'warning',
    message: 'HydroJug Sleeve Blue will run out in 12 days',
    action: 'Order by Dec 18th',
  },
];

// Mock recent activity data
const mockRecentActivity = [
  {
    id: 1,
    type: 'order',
    description: 'Purchase order #PO-2024-001 created for HydroJug Classic 32oz',
    timestamp: '2 hours ago',
  },
  {
    id: 2,
    type: 'delivery',
    description: 'Shipment #SH-2024-045 delivered - HydroJug Pro 40oz (150 units)',
    timestamp: '4 hours ago',
  },
  {
    id: 3,
    type: 'inventory',
    description: 'Stock adjustment for HydroJug Sleeve Blue (+25 units)',
    timestamp: '6 hours ago',
  },
  {
    id: 4,
    type: 'order',
    description: 'Purchase order #PO-2024-002 approved for HydroJug Handle Black',
    timestamp: '8 hours ago',
  },
];

// Simple metric card component matching other pages
function MetricCard({ title, value, subtitle, color = 'primary' }) {
  return (
    <Card>
      <CardContent>
        <Typography color="textSecondary" gutterBottom>
          {title}
        </Typography>
        <Typography variant="h4" sx={{ fontWeight: 600, color: color === 'error' ? 'error.main' : color === 'warning' ? 'warning.main' : 'inherit' }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
}

function Dashboard() {
  const navigate = useNavigate();
  const [metrics] = useState(mockMetrics);
  const [alerts] = useState(mockAlerts);
  const [recentActivity] = useState(mockRecentActivity);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getAlertSeverity = (type) => {
    switch (type) {
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
        gap={{ xs: 2, sm: 0 }}
        mb={3}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Supply Chain Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/inventory')}
          size="small"
          sx={{
            alignSelf: { xs: 'stretch', sm: 'auto' },
            minWidth: { xs: 'auto', sm: 'auto' }
          }}
        >
          Add Inventory
        </Button>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            title="Total Inventory Value"
            value={formatCurrency(metrics.totalInventoryValue)}
            subtitle="Across all locations"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            title="Low Stock Alerts"
            value={metrics.lowStockAlerts}
            color="error"
            subtitle="Require immediate attention"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            title="Pending Orders"
            value={metrics.pendingOrders}
            subtitle="Awaiting fulfillment"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <MetricCard
            title="Active Suppliers"
            value={metrics.activeSuppliers}
            subtitle="Currently engaged"
          />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Button
            variant="contained"
            startIcon={<ShoppingCart />}
            onClick={() => navigate('/orders')}
            fullWidth
            sx={{
              py: { xs: 1.25, sm: 1.5 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Create Order
          </Button>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Button
            variant="outlined"
            startIcon={<Assessment />}
            onClick={() => navigate('/reports')}
            fullWidth
            sx={{
              py: { xs: 1.25, sm: 1.5 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            View Reports
          </Button>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Button
            variant="outlined"
            startIcon={<TrendingUp />}
            onClick={() => navigate('/analytics')}
            fullWidth
            sx={{
              py: { xs: 1.25, sm: 1.5 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Analytics
          </Button>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Button
            variant="outlined"
            startIcon={<Inventory />}
            onClick={() => navigate('/suppliers')}
            fullWidth
            sx={{
              py: { xs: 1.25, sm: 1.5 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Suppliers
          </Button>
        </Grid>
      </Grid>

      {/* Content Sections */}
      <Grid container spacing={{ xs: 2, sm: 3 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
              >
                Inventory Alerts
              </Typography>
              {alerts.slice(0, 3).map((alert) => (
                <Alert
                  key={alert.id}
                  severity={getAlertSeverity(alert.type)}
                  sx={{
                    mb: 1,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 600,
                      fontSize: { xs: '0.8125rem', sm: '0.875rem' }
                    }}
                  >
                    {alert.message}
                  </Typography>
                  {alert.action && (
                    <Typography
                      variant="caption"
                      color="textSecondary"
                      sx={{ fontSize: { xs: '0.75rem', sm: '0.8125rem' } }}
                    >
                      {alert.action}
                    </Typography>
                  )}
                </Alert>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
              >
                Recent Activity
              </Typography>
              <List sx={{ py: 0 }}>
                {recentActivity.slice(0, 4).map((activity) => (
                  <ListItem
                    key={activity.id}
                    sx={{
                      px: 0,
                      py: { xs: 1, sm: 1.5 }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: { xs: 36, sm: 56 } }}>
                      {activity.type === 'order' && <ShoppingCart color="primary" />}
                      {activity.type === 'delivery' && <LocalShipping color="success" />}
                      {activity.type === 'inventory' && <Inventory color="warning" />}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: { xs: '0.8125rem', sm: '0.875rem' },
                            lineHeight: 1.4
                          }}
                        >
                          {activity.description}
                        </Typography>
                      }
                      secondary={
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                          }}
                        >
                          {activity.timestamp}
                        </Typography>
                      }
                    />
                    <Chip
                      label={activity.type}
                      size="small"
                      color={activity.type === 'order' ? 'primary' : activity.type === 'delivery' ? 'success' : 'warning'}
                      sx={{
                        fontSize: { xs: '0.6875rem', sm: '0.75rem' },
                        height: { xs: 20, sm: 24 }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
