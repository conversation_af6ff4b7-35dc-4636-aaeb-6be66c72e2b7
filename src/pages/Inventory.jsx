import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Warning,
  CheckCircle,
  Error,
  FilterList,
} from '@mui/icons-material';

// Mock inventory data
const mockInventory = [
  {
    id: 1,
    sku: 'HJ-CL-32-BLU',
    name: 'HydroJug Classic 32oz',
    category: 'Water Bottles',
    currentStock: 15,
    reorderPoint: 25,
    maxStock: 100,
    unitCost: 24.99,
    location: 'Warehouse A',
    supplier: 'ABC Manufacturing',
    lastUpdated: '2024-01-15',
  },
  {
    id: 2,
    sku: 'HJ-PR-40-BLK',
    name: 'HydroJug Pro 40oz',
    category: 'Water Bottles',
    currentStock: 45,
    reorderPoint: 30,
    maxStock: 150,
    unitCost: 29.99,
    location: 'Warehouse A',
    supplier: 'XYZ Corp',
    lastUpdated: '2024-01-14',
  },
  {
    id: 3,
    sku: 'HJ-LID-STD',
    name: 'Standard Lid',
    category: 'Accessories',
    currentStock: 200,
    reorderPoint: 50,
    maxStock: 500,
    unitCost: 4.99,
    location: 'Warehouse B',
    supplier: 'Parts Plus',
    lastUpdated: '2024-01-13',
  },
  {
    id: 4,
    sku: 'HJ-STR-SIL',
    name: 'Silicone Straw',
    category: 'Accessories',
    currentStock: 8,
    reorderPoint: 20,
    maxStock: 200,
    unitCost: 2.99,
    location: 'Warehouse B',
    supplier: 'Parts Plus',
    lastUpdated: '2024-01-12',
  },
];

function Inventory() {
  const [inventory, setInventory] = useState(mockInventory);
  const [filteredInventory, setFilteredInventory] = useState(mockInventory);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [stockFilter, setStockFilter] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Filter inventory based on search and filters
  useEffect(() => {
    let filtered = inventory;

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (categoryFilter) {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    if (stockFilter) {
      filtered = filtered.filter(item => {
        switch (stockFilter) {
          case 'low':
            return item.currentStock <= item.reorderPoint;
          case 'normal':
            return item.currentStock > item.reorderPoint && item.currentStock < item.maxStock * 0.8;
          case 'high':
            return item.currentStock >= item.maxStock * 0.8;
          default:
            return true;
        }
      });
    }

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, categoryFilter, stockFilter]);

  const getStockStatus = (item) => {
    if (item.currentStock <= item.reorderPoint) {
      return { label: 'Low Stock', color: 'error', icon: <Error /> };
    } else if (item.currentStock >= item.maxStock * 0.8) {
      return { label: 'High Stock', color: 'warning', icon: <Warning /> };
    } else {
      return { label: 'Normal', color: 'success', icon: <CheckCircle /> };
    }
  };

  const handleEditItem = (item) => {
    setSelectedItem(item);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
  };

  const categories = [...new Set(inventory.map(item => item.category))];

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
        gap={{ xs: 2, sm: 0 }}
        mb={3}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}
        >
          Inventory Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpenDialog(true)}
          size="small"
          sx={{
            alignSelf: { xs: 'stretch', sm: 'auto' }
          }}
        >
          Add Product
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Products
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {inventory.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Low Stock Items
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'error.main' }}>
                {inventory.filter(item => item.currentStock <= item.reorderPoint).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Value
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                ${inventory.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0).toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Categories
              </Typography>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {categories.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ p: { xs: 2, sm: 3 }, mb: { xs: 2, sm: 3 } }}>
        <Grid container spacing={{ xs: 2, sm: 2 }} alignItems="center">
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              placeholder="Search by name or SKU..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }
              }}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                label="Category"
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Stock Level</InputLabel>
              <Select
                value={stockFilter}
                label="Stock Level"
                onChange={(e) => setStockFilter(e.target.value)}
              >
                <MenuItem value="">All Levels</MenuItem>
                <MenuItem value="low">Low Stock</MenuItem>
                <MenuItem value="normal">Normal Stock</MenuItem>
                <MenuItem value="high">High Stock</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterList />}
              size="small"
              onClick={() => {
                setSearchTerm('');
                setCategoryFilter('');
                setStockFilter('');
              }}
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' }
              }}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Inventory Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>SKU</TableCell>
              <TableCell>Product Name</TableCell>
              <TableCell>Category</TableCell>
              <TableCell align="right">Current Stock</TableCell>
              <TableCell align="right">Reorder Point</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Location</TableCell>
              <TableCell align="right">Unit Cost</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredInventory.map((item) => {
              const status = getStockStatus(item);
              return (
                <TableRow key={item.id} hover>
                  <TableCell sx={{ fontFamily: 'monospace' }}>{item.sku}</TableCell>
                  <TableCell sx={{ fontWeight: 500 }}>{item.name}</TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600 }}>
                    {item.currentStock}
                  </TableCell>
                  <TableCell align="right">{item.reorderPoint}</TableCell>
                  <TableCell>
                    <Chip
                      label={status.label}
                      color={status.color}
                      size="small"
                      icon={status.icon}
                    />
                  </TableCell>
                  <TableCell>{item.location}</TableCell>
                  <TableCell align="right">${item.unitCost}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleEditItem(item)}
                      color="primary"
                    >
                      <Edit />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedItem ? 'Edit Product' : 'Add New Product'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="SKU"
                defaultValue={selectedItem?.sku || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Product Name"
                defaultValue={selectedItem?.name || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  defaultValue={selectedItem?.category || ''}
                  label="Category"
                >
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Location"
                defaultValue={selectedItem?.location || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <TextField
                fullWidth
                label="Current Stock"
                type="number"
                defaultValue={selectedItem?.currentStock || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <TextField
                fullWidth
                label="Reorder Point"
                type="number"
                defaultValue={selectedItem?.reorderPoint || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <TextField
                fullWidth
                label="Unit Cost"
                type="number"
                step="0.01"
                defaultValue={selectedItem?.unitCost || ''}
                slotProps={{
                  input: {
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleCloseDialog}>
            {selectedItem ? 'Update' : 'Add'} Product
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Inventory;
