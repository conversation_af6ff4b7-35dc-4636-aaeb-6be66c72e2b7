# Firebase Setup Guide for Hydrojug Dashboard

This guide will help you set up Firebase for the Hydrojug Supply Chain Dashboard.

## 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project"
3. Enter project name: `hydrojug-dashboard`
4. Enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Authentication

1. In the Firebase console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Click "Save"

## 3. Create Firestore Database

1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location close to your users
5. Click "Done"

## 4. Get Firebase Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon (</>) to add a web app
4. Register app with name: "Hydrojug Dashboard"
5. Copy the configuration object

## 5. Update Configuration File

Replace the configuration in `src/config/firebase.js`:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

## 6. Create Demo Users (Optional)

You can create demo users through the Authentication tab:

1. Go to Authentication > Users
2. Click "Add user"
3. Create users with these emails:
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>

## 7. Set up Firestore Security Rules

Go to Firestore Database > Rules and update with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Authenticated users can read/write products, suppliers, orders
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 8. Seed Sample Data (Optional)

You can use the built-in data seeding function:

```javascript
import firestoreService from './src/services/firestoreService';

// Call this once to populate sample data
firestoreService.seedDemoData();
```

## 9. BigQuery Setup (Optional)

For advanced analytics:

1. Enable BigQuery API in Google Cloud Console
2. Create a BigQuery dataset named `supply_chain`
3. Set up Firestore to BigQuery export
4. Update BigQuery configuration in the service

## 10. Deploy to Firebase Hosting (Optional)

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize: `firebase init`
4. Build: `npm run build`
5. Deploy: `firebase deploy`

## Troubleshooting

### Common Issues:

1. **Authentication not working**: Check if Email/Password is enabled
2. **Firestore permission denied**: Update security rules
3. **App not loading**: Verify Firebase config is correct
4. **Build errors**: Ensure all dependencies are installed

### Environment Variables

For production, consider using environment variables:

```bash
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-domain
VITE_FIREBASE_PROJECT_ID=your-project-id
```

Then update `firebase.js` to use:

```javascript
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  // ... other config
};
```

## Support

If you encounter issues, check:
- Firebase Console for error logs
- Browser developer tools for client-side errors
- Firebase documentation: https://firebase.google.com/docs
